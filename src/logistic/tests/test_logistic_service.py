import pytest

from pytest_cases import parametrize_with_cases

from logistic.constants import AvailableServicesChoices
from logistic.schema import AvailableService, DeliveryMethod, DeliveryMethodList
from logistic.services.logistic_service import LogisticService
from pricing_v3.services.price_calculators import CartPriceCalculator
from services.services.old_sofa_collection import OldSofaCollectionService
from services.services.white_gloves_delivery import WhiteGlovesDeliveryService


class TestLogisticServiceCases:
    def case_sotty_with_sku(self, cart_factory, cart_item_factory):
        cart = cart_factory(items=[], assembly=True)
        sku_variant_item = cart_item_factory(
            cart=cart, is_skuvariant=True, assembly_price=0
        )
        sotty_item = cart_item_factory(cart=cart, is_sotty=True)

        white_gloves_delivery_item = WhiteGlovesDeliveryService(cart).add_service()
        old_sofa_collection_item = OldSofaCollectionService(cart).add_service()
        CartPriceCalculator(cart).calculate()

        sku_variant_item.refresh_from_db()
        sotty_item.refresh_from_db()
        white_gloves_delivery_item.refresh_from_db()
        old_sofa_collection_item.refresh_from_db()

        expected_delivery_info = DeliveryMethodList(
            [
                DeliveryMethod(
                    estimated_delivery_time='Ships in 3-4 weeks',
                    item_ids=[sotty_item.id],
                    available_services=[
                        AvailableService(
                            service_name=AvailableServicesChoices.DELIVERY,
                            price=sotty_item.aggregate_region_delivery_price,
                            promo_price=sotty_item.aggregate_region_delivery_price
                            - sotty_item.aggregate_region_delivery_promo_value,
                            active=True,
                        ),
                        AvailableService(
                            service_name=AvailableServicesChoices.WHITE_GLOVES_DELIVERY,
                            price=white_gloves_delivery_item.aggregate_region_price,
                            promo_price=white_gloves_delivery_item.aggregate_region_price,
                            active=True,
                        ),
                        AvailableService(
                            service_name=AvailableServicesChoices.OLD_SOFA_COLLECTION,
                            price=old_sofa_collection_item.aggregate_region_price,
                            promo_price=old_sofa_collection_item.aggregate_region_price,
                            active=True,
                        ),
                    ],
                ),
                DeliveryMethod(
                    estimated_delivery_time='Ships in 1-2 days',
                    item_ids=[sku_variant_item.id],
                    available_services=[
                        AvailableService(
                            service_name=AvailableServicesChoices.DELIVERY,
                            price=sku_variant_item.aggregate_region_delivery_price,
                            promo_price=sku_variant_item.aggregate_region_delivery_promo_value,
                            active=True,
                        )
                    ],
                ),
            ]
        )
        return cart, expected_delivery_info.model_dump_json()

    @pytest.mark.parametrize('assembly_enabled', [True, False])
    def case_jetty_with_sku(self, assembly_enabled, cart_factory, cart_item_factory):
        cart = cart_factory(items=[], assembly=assembly_enabled)
        sku_variant_item = cart_item_factory(
            cart=cart, is_skuvariant=True, assembly_price=0
        )
        jetty_item = cart_item_factory(cart=cart, is_jetty=True)

        CartPriceCalculator(cart).calculate()

        sku_variant_item.refresh_from_db()
        jetty_item.refresh_from_db()

        expected_delivery_info = DeliveryMethodList(
            root=[
                DeliveryMethod(
                    estimated_delivery_time='Ships in 3-4 weeks',
                    item_ids=[jetty_item.id],
                    available_services=[
                        AvailableService(
                            service_name=AvailableServicesChoices.DELIVERY,
                            price=jetty_item.aggregate_region_delivery_price,
                            promo_price=jetty_item.aggregate_region_delivery_price
                            - jetty_item.aggregate_region_delivery_promo_value,
                            active=True,
                        ),
                        AvailableService(
                            service_name=AvailableServicesChoices.ASSEMBLY,
                            price=jetty_item.aggregate_region_assembly_price,
                            promo_price=jetty_item.aggregate_region_assembly_price,
                            active=assembly_enabled,
                        ),
                    ],
                ),
                DeliveryMethod(
                    estimated_delivery_time='Ships in 1-2 days',
                    item_ids=[sku_variant_item.id],
                    available_services=[
                        AvailableService(
                            service_name=AvailableServicesChoices.DELIVERY,
                            price=sku_variant_item.aggregate_region_delivery_price,
                            promo_price=sku_variant_item.aggregate_region_delivery_promo_value,
                            active=True,
                        )
                    ],
                ),
            ]
        )
        return cart, expected_delivery_info.model_dump_json()

    @pytest.mark.parametrize('assembly_enabled', [True, False])
    def case_empty_cart(self, assembly_enabled, cart_factory):
        cart = cart_factory(items=[], assembly=assembly_enabled)
        expected_delivery_info = DeliveryMethodList(root=[])
        return cart, expected_delivery_info.model_dump_json()


class TestLogisticServiceBase:
    @parametrize_with_cases(
        ('cart', 'expected_delivery_info'),
        cases=TestLogisticServiceCases,
    )
    def test_logistic_cart_service_get_delivery_info(
        self, cart, expected_delivery_info
    ):
        logistic_cart_service = LogisticService(instance=cart)
        delivery_info = logistic_cart_service.get_delivery_methods()
        assert delivery_info.model_dump_json() == expected_delivery_info
