from celery import shared_task

from logistic.schema import DeliveryMethod
from logistic.services.delivery_bucket_creator import create_delivery_bucket
from logistic.services.logistic_service import LogisticService
from orders.models import Order


@shared_task
def process_delivery_buckets(order_id: int) -> None:
    order = Order.objects.get(id=order_id)
    delivery_methods = LogisticService(instance=order).get_delivery_methods()
    if len(delivery_methods) == 1:
        # don't create buckets if there is only one transport
        return

    for method in delivery_methods:
        create_delivery_bucket_task.delay(
            delivery_method=method.model_dump(),
            region_name=order.region.name,
        )


@shared_task
def create_delivery_bucket_task(delivery_method: dict, region_name: str) -> None:
    create_delivery_bucket(
        delivery_method=DeliveryMethod(**delivery_method),
        region_name=region_name,
    )
