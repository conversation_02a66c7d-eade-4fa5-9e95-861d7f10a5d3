from django.db import models

from custom.models import Timestampable
from logistic.constants import DeliveryOperator


class DeliveryBucket(Timestampable):
    """Store items form one order what will be delivered separately."""
    order_items = models.ManyToManyField(
        'orders.OrderItem',
        related_name='delivery_bucket',
    )
    declared_delivery_time = models.CharField()
    operator = models.CharField(choices=DeliveryOperator.choices)

    class Meta:
        verbose_name = 'Delivery Bucket'
        verbose_name_plural = 'Delivery Buckets'
