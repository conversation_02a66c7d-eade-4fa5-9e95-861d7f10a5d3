import socket

from urllib.parse import urljoin

from .base import *

# DEBUG
DEBUG = env.bool('DJANGO_DEBUG', default=True)


# SECRET KEY
SECRET_KEY = env.str(
    'DJANGO_SECRET_KEY',
    default='localro5t2z1d*^_5s=@j=&n)rio@c59gh+8^bh6!(kk-5=1s2t)dxz',
)

CSRF_TRUSTED_ORIGINS = ['http://localhost:8000', 'https://*.ngrok-free.app']
# FIXTURES
FIXTURE_DIRS = [
    str(APPS_DIR.path('fixtures/')),
]
# TEMPLATES
TEMPLATES[0]['OPTIONS']['debug'] = DEBUG
TEMPLATES[0]['OPTIONS']['loaders'] = [
    'django.template.loaders.filesystem.Loader',
    'django.template.loaders.app_directories.Loader',
]


# EMAILS
EMAIL_BACKEND = 'django_mailer.smtp_queue.EmailBackend'


# HTTP
ALLOWED_HOSTS = ['*']
INTERNAL_IPS = ['127.0.0.1', '0.0.0.0']  # noqa: S104


# STATIC FILES
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'


# WEBPACK LOADER
WEBPACK_LOADER['DEFAULT']['CACHE'] = not DEBUG
WEBPACK_LOADER['ES5']['CACHE'] = not DEBUG


# REST FRAMEWORK
REST_FRAMEWORK['DEFAULT_THROTTLE_RATES'] = {
    'anon': '10/s',
    'user': '30/s',
    'heavy_throttle': '50/hour',
    'production_files': '50/s',
}
REST_FRAMEWORK['DEFAULT_RENDERER_CLASSES'] += (
    'rest_framework.renderers.BrowsableAPIRenderer',
)
REST_FRAMEWORK['ECOMMERCE_RENDERER_CLASSES'] += (
    'djangorestframework_camel_case.render.CamelCaseBrowsableAPIRenderer',
)


# DEBUG TOOLBAR
DEBUG_TOOLBAR = env.bool('DEBUG_TOOLBAR', default=False)
if DEBUG_TOOLBAR:
    INSTALLED_APPS.append('debug_toolbar')
    MIDDLEWARE.append('debug_toolbar.middleware.DebugToolbarMiddleware')
    # use debug toolbar when developing with docker
    try:
        ip = socket.gethostbyname(socket.gethostname())
        INTERNAL_IPS.append(ip[:-1] + '1')
    except socket.gaierror:
        # exception for MACOS
        pass
    DEBUG_TOOLBAR_CONFIG = {
        'DISABLE_PANELS': [
            'debug_toolbar.panels.redirects.RedirectsPanel',
            'debug_toolbar.panels.versions.VersionsPanel',
        ],
        'SHOW_TEMPLATE_CONTEXT': True,
        'SHOW_TOOLBAR_CALLBACK': lambda request: True,  # show with local nginx
    }


# DJANGO EXTENSIONS
IPYTHON_ARGUMENTS = env.list('IPYTHON_ARGUMENTS', default=[])
SHELL_PLUS_PRE_IMPORTS = ['logger.checker']


# SILK
USE_SILK = env.bool('USE_SILK', default=False)
SILKY_MAX_RESPONSE_BODY_SIZE = env.bool('SILKY_MAX_RESPONSE_BODY_SIZE', default=1024)
# NOTE: run migrations after enabling silk
if USE_SILK:
    INSTALLED_APPS.append('silk')
    MIDDLEWARE.append('silk.middleware.SilkyMiddleware')
    SILKY_INTERCEPT_FUNC = (  # noqa: E731
        lambda request: 'uploaded' not in request.get_full_path()
    )
    SILKY_PYTHON_PROFILER = env.str('SILKY_PYTHON_PROFILER', False)


# DEVSERVER
DEVSERVER_MODULES = (
    'devserver.modules.sql.SQLRealTimeModule',
    'devserver.modules.sql.SQLSummaryModule',
    'devserver.modules.profile.ProfileSummaryModule',
    'devserver.modules.cache.CacheSummaryModule',
)
DEVSERVER_ARGS = [
    '--werkzeug',
]


# SORL THUMBNAIL
THUMBNAIL_DEBUG = True


# DJANGO TEST MIGRATIONS
# install Django check that produces warnings for all migrations with
# autogenerated names
INSTALLED_APPS.append('django_test_migrations.contrib.django_checks.AutoNames')


# DJANGO CORS HEADERS
# `django-cors-headers` is needed when developing some vue apps e.g.
# `assembly_service_company_panel` locally
INSTALLED_APPS.append('corsheaders')
MIDDLEWARE.insert(2, 'corsheaders.middleware.CorsMiddleware')
CORS_ALLOWED_ORIGINS = [
    'http://localhost:3333',
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:3333',
    'http://127.0.0.1',
]
CORS_ALLOW_CREDENTIALS = True


# LOCAL GENERAL SETTINGS
IS_TESTING = True
IS_LOCAL = True
IS_DEV = True
IS_PRODUCTION = False

CSTM_PROD_TOKEN = env.str('CSTM_PROD_TOKEN', default='')


# LOCAL GENERAL SETTINGS
# DYNAMIC DELIVERY

CELERY_TASK_ALWAYS_EAGER = env.bool('CELERY_TASK_ALWAYS_EAGER', default=False)

if USE_AWS_S3_MEDIA_STORAGE:
    MINIO_ROOT_USER = env.str('MINIO_ROOT_USER', default='')
    MINIO_ROOT_PASSWORD = env.str('MINIO_ROOT_PASSWORD', default='')

    AWS_ACCESS_KEY_ID = AWS_ACCESS_KEY_ID or MINIO_ROOT_USER
    AWS_SECRET_ACCESS_KEY = AWS_SECRET_ACCESS_KEY or MINIO_ROOT_PASSWORD
    AWS_STORAGE_BUCKET_NAME = env.str(
        'LOCAL_BUCKET_NAME',
        default='my-local-bucket',
    )
    AWS_S3_ENDPOINT_URL = env.str(
        'LOCAL_STORAGE_URL',
        default='http://minio:9000',
    )

# LOGGING
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': (
                '{levelname} {name} {asctime} {pathname} {funcName} {lineno} {message}'
            ),
            'style': '{',
        },
        'celery': {
            '()': 'celery.app.log.TaskFormatter',
            'format': (
                '%(asctime)s - %(task_id)s - %(task_name)s - %(name)s - '
                '%(levelname)s - %(message)s'
            ),
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'null': {
            'class': 'logging.NullHandler',
        },
        'celery_file': {
            'level': 'WARNING',
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': str(APPS_DIR.path('logs/django_celery.log')),
            'formatter': 'celery',
        },
        'celery_task_file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': str(APPS_DIR.path('logs/tasks.log')),
            'formatter': 'celery',
        },
        'celery_producers_task_file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': str(APPS_DIR.path('logs/tasks_producers.log')),
            'formatter': 'celery',
        },
        'celery_invoice_task_file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': str(APPS_DIR.path('logs/tasks_invoice.log')),
            'formatter': 'celery',
        },
        'celery_mailing_task_file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': str(APPS_DIR.path('logs/tasks_mailing.log')),
            'formatter': 'celery',
        },
        'celery_customer_service_task_file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.WatchedFileHandler',
            'filename': str(APPS_DIR.path('logs/tasks_customer_service.log')),
            'formatter': 'celery',
        },
    },
    'loggers': {
        'root': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'matplotlib': {
            'handlers': ['console'],
            'level': 'ERROR',
        },
        'ezdxf': {
            'handlers': ['console'],
            'level': 'ERROR',
        },
        'urllib3.connectionpool': {
            'handlers': ['console'],
            'level': 'ERROR',
        },
        'datadog.threadstats': {
            'handlers': ['console'],
            'level': 'ERROR',
        },
        'datadog.dogstatsd': {
            'handlers': ['console'],
            'level': 'ERROR',
        },
        'datadog.util': {
            'handlers': ['console'],
            'level': 'ERROR',
        },
        'faker.factory': {
            'handlers': ['console'],
            'level': 'ERROR',
        },
        'PIL.Image': {
            'handlers': ['console'],
            'level': 'ERROR',
        },
        'silk': {
            'handlers': ['console'],
            'level': 'ERROR',
        },
        'asyncio': {
            'handlers': ['console'],
            'level': 'ERROR',
        },
        'celery': {
            'handlers': ['celery_file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'celery_task': {
            'handlers': ['celery_task_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'producers.tasks': {
            'handlers': ['celery_producers_task_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'invoice.tasks': {
            'handlers': ['celery_invoice_task_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'mailing.tasks': {
            'handlers': ['celery_mailing_task_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'customer_service.tasks': {
            'handlers': ['celery_customer_service_task_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

MEDIA_URL = urljoin(SITE_URL, MEDIA_URL)

# BRAZE
BRAZE_SMS_SUBSCRIPTION_GROUPS = {'+48': '0bd319e5-9e07-45e8-8116-b1319f30e3db'}
