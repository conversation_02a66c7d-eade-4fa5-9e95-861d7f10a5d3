import pytest

from logistic.constants import DeliveryOperator
from logistic.models import DeliveryBucket
from logistic.schema import AvailableService, DeliveryMethod
from logistic.services.delivery_bucket_creator import (
    create_delivery_bucket,
    determine_delivery_operator,
)
from logistic.constants import AvailableServicesChoices


@pytest.mark.django_db
class TestDetermineDeliveryOperator:
    @pytest.mark.parametrize('region_name,expected_operator', [
        ('austria', DeliveryOperator.FEDEX),
        ('belgium', DeliveryOperator.GLS),
        ('bulgaria', DeliveryOperator.FEDEX),
        ('croatia', DeliveryOperator.FEDEX),
        ('czech', DeliveryOperator.GLS),
        ('denmark', DeliveryOperator.DPD),
        ('estonia', DeliveryOperator.DPD),
        ('finland', DeliveryOperator.DPD),
        ('france', DeliveryOperator.GLS),
        ('germany', DeliveryOperator.DHL_DE),
        ('greece', DeliveryOperator.FEDEX),
        ('hungary', DeliveryOperator.FEDEX),
        ('ireland', DeliveryOperator.FEDEX),
        ('italy', DeliveryOperator.DPD),
        ('latvia', DeliveryOperator.DPD),
        ('lithuania', DeliveryOperator.DPD),
        ('luxembourg', DeliveryOperator.FEDEX),
        ('netherlands', DeliveryOperator.GLS),
        ('norway', DeliveryOperator.FEDEX),
        ('poland', DeliveryOperator.DPD),
        ('portugal', DeliveryOperator.DPD),
        ('romania', DeliveryOperator.FEDEX),
        ('slovakia', DeliveryOperator.FEDEX),
        ('slovenia', DeliveryOperator.DPD),
        ('spain', DeliveryOperator.DPD),
        ('sweden', DeliveryOperator.DPD),
        ('switzerland', DeliveryOperator.FEDEX),
        ('united_kingdom', DeliveryOperator.FEDEX),
    ])
    def test_sku_items_get_region_specific_operator(
        self, region_name, expected_operator, order_item_factory
    ):
        order_item = order_item_factory(is_skuvariant=True)
        delivery_method = DeliveryMethod(
            estimated_delivery_time='Ships in 1-2 days',
            item_ids=[order_item.id],
            available_services=[],
        )

        operator = determine_delivery_operator(delivery_method, region_name)

        assert operator == expected_operator

    def test_non_sku_items_get_dedicated_transport(self, order_item_factory):
        order_item = order_item_factory(is_jetty=True)
        delivery_method = DeliveryMethod(
            estimated_delivery_time='Ships in 3-4 weeks',
            item_ids=[order_item.id],
            available_services=[],
        )

        operator = determine_delivery_operator(delivery_method, 'germany')

        assert operator == DeliveryOperator.DEDICATED_TRANSPORT

    def test_mixed_items_get_dedicated_transport(self, order_item_factory):
        sku_item = order_item_factory(is_skuvariant=True)
        jetty_item = order_item_factory(is_jetty=True)
        delivery_method = DeliveryMethod(
            estimated_delivery_time='Ships in 2-3 weeks',
            item_ids=[sku_item.id, jetty_item.id],
            available_services=[],
        )

        operator = determine_delivery_operator(delivery_method, 'germany')

        assert operator == DeliveryOperator.DEDICATED_TRANSPORT

    def test_unknown_region_raises_error(self, order_item_factory):
        order_item = order_item_factory(is_skuvariant=True)
        delivery_method = DeliveryMethod(
            estimated_delivery_time='Ships in 1-2 days',
            item_ids=[order_item.id],
            available_services=[],
        )
        with pytest.raises(KeyError):
            determine_delivery_operator(delivery_method, 'unknown_region')


@pytest.mark.django_db
class TestCreateDeliveryBucket:
    def test_creates_bucket_with_correct_attributes(self, order_item_factory):
        order_item = order_item_factory(is_skuvariant=True)
        delivery_method = DeliveryMethod(
            estimated_delivery_time='Ships in 1-2 days',
            item_ids=[order_item.id],
            available_services=[
                AvailableService(
                    service_name=AvailableServicesChoices.DELIVERY,
                    price=50.00,
                    promo_price=45.00,
                    active=True,
                )
            ],
        )

        create_delivery_bucket(delivery_method, 'germany')

        bucket = DeliveryBucket.objects.get()
        assert bucket.declared_delivery_time == 'Ships in 1-2 days'
        assert bucket.operator == DeliveryOperator.DHL_DE
        assert list(bucket.order_items.all()) == [order_item]

    def test_creates_bucket_for_non_sku_items(self, order_item_factory):
        order_item = order_item_factory(is_jetty=True)
        delivery_method = DeliveryMethod(
            estimated_delivery_time='Ships in 3-4 weeks',
            item_ids=[order_item.id],
            available_services=[],
        )

        create_delivery_bucket(delivery_method, 'germany')

        bucket = DeliveryBucket.objects.get()
        assert bucket.declared_delivery_time == 'Ships in 3-4 weeks'
        assert bucket.operator == DeliveryOperator.DEDICATED_TRANSPORT
        assert list(bucket.order_items.all()) == [order_item]

    def test_creates_bucket_with_multiple_items(self, order_item_factory):
        item1 = order_item_factory(is_skuvariant=True)
        item2 = order_item_factory(is_skuvariant=True)
        delivery_method = DeliveryMethod(
            estimated_delivery_time='Ships in 1-2 days',
            item_ids=[item1.id, item2.id],
            available_services=[],
        )

        create_delivery_bucket(delivery_method, 'france')

        bucket = DeliveryBucket.objects.get()
        assert bucket.operator == DeliveryOperator.GLS
        assert set(bucket.order_items.all()) == {item1, item2}

    @pytest.mark.parametrize('region_name,expected_operator', [
        ('austria', DeliveryOperator.FEDEX),
        ('belgium', DeliveryOperator.GLS),
        ('bulgaria', DeliveryOperator.FEDEX),
        ('croatia', DeliveryOperator.FEDEX),
        ('czech', DeliveryOperator.GLS),
        ('denmark', DeliveryOperator.DPD),
        ('estonia', DeliveryOperator.DPD),
        ('finland', DeliveryOperator.DPD),
        ('france', DeliveryOperator.GLS),
        ('germany', DeliveryOperator.DHL_DE),
        ('greece', DeliveryOperator.FEDEX),
        ('hungary', DeliveryOperator.FEDEX),
        ('ireland', DeliveryOperator.FEDEX),
        ('italy', DeliveryOperator.DPD),
        ('latvia', DeliveryOperator.DPD),
        ('lithuania', DeliveryOperator.DPD),
        ('luxembourg', DeliveryOperator.FEDEX),
        ('netherlands', DeliveryOperator.GLS),
        ('norway', DeliveryOperator.FEDEX),
        ('poland', DeliveryOperator.DPD),
        ('portugal', DeliveryOperator.DPD),
        ('romania', DeliveryOperator.FEDEX),
        ('slovakia', DeliveryOperator.FEDEX),
        ('slovenia', DeliveryOperator.DPD),
        ('spain', DeliveryOperator.DPD),
        ('sweden', DeliveryOperator.DPD),
        ('switzerland', DeliveryOperator.FEDEX),
        ('united_kingdom', DeliveryOperator.FEDEX),
    ])
    def test_creates_bucket_for_all_supported_regions(
        self, region_name, expected_operator, order_item_factory
    ):
        order_item = order_item_factory(is_skuvariant=True)
        delivery_method = DeliveryMethod(
            estimated_delivery_time='Ships in 1-2 days',
            item_ids=[order_item.id],
            available_services=[],
        )

        create_delivery_bucket(delivery_method, region_name)

        bucket = DeliveryBucket.objects.get()
        assert bucket.operator == expected_operator
