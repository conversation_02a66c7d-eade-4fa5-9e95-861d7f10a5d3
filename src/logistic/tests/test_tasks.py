import pytest
from unittest.mock import patch

from logistic.models import DeliveryBucket
from logistic.schema import AvailableService, DeliveryMethod, DeliveryMethodList
from logistic.tasks import process_delivery_buckets, create_delivery_bucket_task
from logistic.constants import AvailableServicesChoices, DeliveryOperator


@pytest.mark.django_db
class TestCreateDeliveryBuckets:
    def test_single_delivery_method_does_not_create_buckets(
        self, order_factory, order_item_factory, region_factory, mocker
    ):
        region = region_factory(name='germany')
        order = order_factory(region=region)
        order_item_factory(order=order, is_skuvariant=True)

        mock_get_delivery_methods = mocker.patch(
            'logistic.services.logistic_service.LogisticService.get_delivery_methods'
        )
        mock_get_delivery_methods.return_value = DeliveryMethodList([
            DeliveryMethod(
                estimated_delivery_time='Ships in 1-2 days',
                item_ids=[1],
                available_services=[],
            )
        ])

        with patch('logistic.tasks.create_delivery_bucket_task.delay') as mock_delay:
            process_delivery_buckets(order.id)

            mock_delay.assert_not_called()

    def test_multiple_delivery_methods_create_buckets(
        self, order_factory, order_item_factory, region_factory, mocker
    ):
        region = region_factory(name='germany')
        order = order_factory(region=region)
        sku_item = order_item_factory(order=order, is_skuvariant=True)
        jetty_item = order_item_factory(order=order, is_jetty=True)

        mock_get_delivery_methods = mocker.patch(
            'logistic.services.logistic_service.LogisticService.get_delivery_methods'
        )
        mock_get_delivery_methods.return_value = DeliveryMethodList([
            DeliveryMethod(
                estimated_delivery_time='Ships in 1-2 days',
                item_ids=[sku_item.id],
                available_services=[
                    AvailableService(
                        service_name=AvailableServicesChoices.DELIVERY,
                        price=50.00,
                        promo_price=45.00,
                        active=True,
                    )
                ],
            ),
            DeliveryMethod(
                estimated_delivery_time='Ships in 3-4 weeks',
                item_ids=[jetty_item.id],
                available_services=[
                    AvailableService(
                        service_name=AvailableServicesChoices.DELIVERY,
                        price=100.00,
                        promo_price=90.00,
                        active=True,
                    )
                ],
            ),
        ])

        with patch('logistic.tasks.create_delivery_bucket_task.delay') as mock_delay:
            process_delivery_buckets(order.id)

            assert mock_delay.call_count == 2

            call_args_list = mock_delay.call_args_list
            assert call_args_list[0][1]['region_name'] == 'germany'
            assert call_args_list[1][1]['region_name'] == 'germany'

            first_method = call_args_list[0][1]['delivery_method']
            second_method = call_args_list[1][1]['delivery_method']

            assert first_method['estimated_delivery_time'] == 'Ships in 1-2 days'
            assert second_method['estimated_delivery_time'] == 'Ships in 3-4 weeks'

    @pytest.mark.parametrize('region_name', [
        'poland', 'france', 'austria', 'belgium', 'netherlands'
    ])
    def test_creates_buckets_for_different_regions(
        self, region_name, order_factory, order_item_factory, region_factory, mocker
    ):
        region = region_factory(name=region_name)
        order = order_factory(region=region)
        sku_item = order_item_factory(order=order, is_skuvariant=True)
        jetty_item = order_item_factory(order=order, is_jetty=True)

        mock_get_delivery_methods = mocker.patch(
            'logistic.services.logistic_service.LogisticService.get_delivery_methods'
        )
        mock_get_delivery_methods.return_value = DeliveryMethodList([
            DeliveryMethod(
                estimated_delivery_time='Ships in 1-2 days',
                item_ids=[sku_item.id],
                available_services=[],
            ),
            DeliveryMethod(
                estimated_delivery_time='Ships in 3-4 weeks',
                item_ids=[jetty_item.id],
                available_services=[],
            ),
        ])

        with patch('logistic.tasks.create_delivery_bucket_task.delay') as mock_delay:
            process_delivery_buckets(order.id)

            assert mock_delay.call_count == 2
            for call_args in mock_delay.call_args_list:
                assert call_args[1]['region_name'] == region_name


@pytest.mark.django_db
class TestCreateDeliveryBucketTask:
    def test_creates_delivery_bucket_from_dict(self, order_item_factory):
        order_item = order_item_factory(is_skuvariant=True)
        delivery_method_dict = {
            'estimated_delivery_time': 'Ships in 1-2 days',
            'item_ids': [order_item.id],
            'available_services': [
                {
                    'service_name': AvailableServicesChoices.DELIVERY,
                    'price': 50.00,
                    'promo_price': 45.00,
                    'active': True,
                }
            ],
        }

        create_delivery_bucket_task(delivery_method_dict, 'germany')

        bucket = DeliveryBucket.objects.get()
        assert bucket.declared_delivery_time == 'Ships in 1-2 days'
        assert bucket.operator == DeliveryOperator.DHL_DE
        assert list(bucket.order_items.all()) == [order_item]

    def test_creates_bucket_for_multiple_items(self, order_item_factory):
        item1 = order_item_factory(is_skuvariant=True)
        item2 = order_item_factory(is_skuvariant=True)
        delivery_method_dict = {
            'estimated_delivery_time': 'Ships in 1-2 days',
            'item_ids': [item1.id, item2.id],
            'available_services': [],
        }

        create_delivery_bucket_task(delivery_method_dict, 'france')

        bucket = DeliveryBucket.objects.get()
        assert bucket.operator == DeliveryOperator.GLS
        assert set(bucket.order_items.all()) == {item1, item2}

    @pytest.mark.parametrize('region_name,expected_operator', [
        ('germany', DeliveryOperator.DHL_DE),
        ('france', DeliveryOperator.GLS),
        ('poland', DeliveryOperator.DPD),
        ('austria', DeliveryOperator.FEDEX),
    ])
    def test_creates_bucket_with_correct_operator_for_region(
        self, region_name, expected_operator, order_item_factory
    ):
        order_item = order_item_factory(is_skuvariant=True)
        delivery_method_dict = {
            'estimated_delivery_time': 'Ships in 1-2 days',
            'item_ids': [order_item.id],
            'available_services': [],
        }

        create_delivery_bucket_task(delivery_method_dict, region_name)

        bucket = DeliveryBucket.objects.get()
        assert bucket.operator == expected_operator

    def test_creates_bucket_for_non_sku_items(self, order_item_factory):
        order_item = order_item_factory(is_jetty=True)
        delivery_method_dict = {
            'estimated_delivery_time': 'Ships in 3-4 weeks',
            'item_ids': [order_item.id],
            'available_services': [],
        }

        create_delivery_bucket_task(delivery_method_dict, 'germany')

        bucket = DeliveryBucket.objects.get()
        assert bucket.operator == DeliveryOperator.DEDICATED_TRANSPORT

        assert bucket.declared_delivery_time == 'Ships in 3-4 weeks'
