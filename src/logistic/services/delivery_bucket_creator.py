from logistic.constants import Delivery<PERSON>perator
from logistic.models import DeliveryBucket
from logistic.schema import DeliveryMethod
from orders.models import OrderItem

def determine_delivery_operator(
    delivery_method: DeliveryMethod,
    region_name: str,
) -> DeliveryOperator:
    are_items_sku = set(OrderItem.objects.filter(
        id__in=delivery_method.item_ids,
    ).values_list('content_type__model', flat=True)) == {'skuvariant'}

    if are_items_sku:
        return DeliveryOperator.default_operator_for_region(region_name)
    return DeliveryOperator.DEDICATED_TRANSPORT

def create_delivery_bucket(delivery_method: DeliveryMethod, region_name: str) -> None:
    bucket = DeliveryBucket.objects.create(
        declared_delivery_time=delivery_method.estimated_delivery_time,
        operator=determine_delivery_operator(delivery_method, region_name),
    )
    bucket.order_items.set(delivery_method.item_ids)



