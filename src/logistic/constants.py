import enum

from django.db.models import TextChoices


class AvailableServicesChoices(enum.StrEnum):
    DELIVERY = 'delivery'
    ASSEMBLY = 'assembly'
    WHITE_GLOVES_DELIVERY = 'white_gloves_delivery'
    OLD_SOFA_COLLECTION = 'old_sofa_collection'


class DeliveryOperator(TextChoices):
    DEDICATED_TRANSPORT = 'dedicated_transport'
    DHL_DE = 'dhl_de'
    GLS = 'gls'
    DPD = 'dpd'
    FEDEX = 'fedex'


    @classmethod
    def default_operator_for_region(cls, region_name: str) -> 'DeliveryOperator':
        return {
            'austria': cls.FEDEX,
            'belgium': cls.GLS,
            'bulgaria': cls.FEDEX,
            'croatia': cls.FEDEX,
            'czech': cls.GLS,
            'denmark': cls.DPD,
            'estonia': cls.DPD,
            'finland': cls.DPD,
            'france': cls.GLS,
            'germany': cls.DHL_DE,
            'greece': cls.FEDEX,
            'hungary': cls.FEDEX,
            'ireland': cls.FEDEX,
            'italy': cls.DPD,
            'latvia': cls.DPD,
            'lithuania': cls.DPD,
            'luxembourg': cls.FEDEX,
            'netherlands': cls.GLS,
            'norway': cls.FEDEX,
            'poland': cls.DPD,
            'portugal': cls.DPD,
            'romania': cls.FEDEX,
            'slovakia': cls.FEDEX,
            'slovenia': cls.DPD,
            'spain': cls.DPD,
            'sweden': cls.DPD,
            'switzerland': cls.FEDEX,
            'united_kingdom': cls.FEDEX,
        }[region_name]
