from decimal import Decimal

from pydantic import BaseModel, RootModel

from logistic.constants import AvailableServicesChoices


class AvailableService(BaseModel):
    service_name: AvailableServicesChoices
    price: Decimal
    promo_price: Decimal
    active: bool


class DeliveryMethod(BaseModel):
    estimated_delivery_time: str
    item_ids: list[int]
    available_services: list[AvailableService]


class DeliveryMethodList(RootModel):
    root: list[DeliveryMethod]

    def __iter__(self):
        return iter(self.root)

    def __getitem__(self, item):
        return self.root[item]

    def __len__(self):
        return len(self.root)
