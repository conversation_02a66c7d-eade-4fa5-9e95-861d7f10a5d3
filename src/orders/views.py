import logging

from time import mktime
from typing import Optional
from urllib.parse import urlencode

from django.conf import settings
from django.contrib.auth.models import User
from django.db.models import prefetch_related_objects
from django.http.response import (
    HttpResponse,
    HttpResponseRedirect,
)
from django.utils import translation
from django.utils.translation import get_language_from_request
from rest_framework import status
from rest_framework.authentication import (
    SessionAuthentication,
    TokenAuthentication,
)
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
from rest_framework.generics import get_object_or_404
from rest_framework.mixins import (
    DestroyModelMixin,
    ListModelMixin,
    RetrieveModelMixin,
    UpdateModelMixin,
)
from rest_framework.permissions import (
    AllowAny,
    IsAdminUser,
    IsAuthenticated,
)
from rest_framework.response import Response
from rest_framework.serializers import BaseSerializer
from rest_framework.views import APIView
from rest_framework.viewsets import (
    GenericViewSet,
    ModelViewSet,
)

import requests

from django_filters.rest_framework import DjangoFilterBackend
from requests.exceptions import ConnectionError

from carts.exceptions import MaxCartSizeError
from carts.services.cart_service import CartService
from custom.enums import LanguageEnum
from custom.models import (
    Countries,
    GlobalSettings,
)
from custom.permissions import (
    BrazePermission,
    IsAuthenticatedAndOwnerExceptPost,
    IsOwner,
)
from custom.utils.adyen import (
    adyen_structure_helper,
    get_current_payment_settings,
)
from custom.utils.url import get_request_source
from gallery.enums import FurnitureStatusEnum
from gallery.services.prices_for_serializers import get_currency_rate
from invoice.serializers import RetoolInvoiceSerializer
from invoice.tasks import create_proforma_invoice_task
from logistic.tasks import process_delivery_buckets
from orders.choices import OrderSource
from orders.enums import (
    OrderStatus,
    OrderType,
)
from orders.filter_sets import OrderByEmailFilter
from orders.models import (
    Order,
    OrderItem,
)
from orders.serializers import (
    CheckOrderStatusFiltersSerializer,
    CheckOrderStatusHistorySerializer,
    CheckOrderStatusSerializer,
    EcommerceOrderSerializer,
    GTMPurchaseEventSerializer,
    MailingOrderSummarySerializer,
    OrderRetoolSerializer,
    OrderSerializer,
    OrderStatusSerializer,
    RetoolCopyItemSerializer,
)
from orders.services.status_description import get_status_description
from orders.tasks import (
    send_order_confirmation_email_messages,
    send_purchase_event_data_to_gtm,
)
from orders.utils import sanitise_postal_code
from payments.models import Transaction
from pricing_v3.services.price_calculators import OrderPriceCalculator
from promotions.utils import (
    get_active_promotion,
    get_strikethrough_promo_data,
)
from regions.cached_region import get_region_data_from_request
from regions.mixins import RegionCalculationsObject
from regions.models import Region
from vouchers.services.voucher_service import VoucherService

logger = logging.getLogger('orders')


class OrderViewSet(
    RetrieveModelMixin,
    UpdateModelMixin,
    DestroyModelMixin,
    ListModelMixin,
    GenericViewSet,
):
    queryset = Order.objects.filter(parent_order__isnull=True)
    permission_classes = (IsAuthenticatedAndOwnerExceptPost,)
    serializer_class = OrderSerializer

    def set_order_source(self, order):
        order.order_source = get_request_source(self.request)

    def get_serializer_context(self):
        region = get_region_data_from_request(self.request)
        return {
            'region': region,
            'currency_rate': get_currency_rate(region),
            **super().get_serializer_context(),
        }

    def perform_update(self, serializer):
        order = serializer.save()
        self.set_order_source(order)
        if 'country' in serializer.validated_data:
            new_country = serializer.validated_data['country']
            region = self.request.user.profile.change_region_by_country(new_country)
            order.region = region
        calculator = OrderPriceCalculator(order)
        calculator.calculate(check_vat=True)
        return calculator.message_status, calculator.message_text

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        data = serializer.data
        if instance.additional_text:
            data['additional_text'] = instance.additional_text
        return Response(data)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        for fn, f in list(serializer.fields.items()):
            if isinstance(f, BaseSerializer):
                serializer.fields[fn].read_only = True
        serializer.is_valid(raise_exception=True)
        (msg_status, msg_text) = self.perform_update(serializer)
        serializer_data = serializer.data
        if msg_status is not None:
            if msg_status == 'error':
                serializer_data['cstm_message_status'] = msg_status
                serializer_data['cstm_message_text'] = msg_text
        return Response(serializer_data)

    def get_queryset(self):
        user = self.request.user
        return Order.objects.filter(owner=user, parent_order__isnull=True)

    def _get_language(self):
        language = LanguageEnum(translation.get_language())
        return (
            language
            if language != LanguageEnum.EN
            else LanguageEnum.get_locale(language, united_states=False)
        )

    @action(detail=True)
    def payment_web_lookup(self, request, pk=None):
        language = self._get_language()
        order = self.get_object()
        live_payment = False
        url = 'https://{environment}.adyen.com/hpp/'.format(
            environment=settings.ADYEN.get('ENVIRONMENT')
        )
        region = order.get_region()
        country_code = region.get_country().code
        currency_code = region.get_currency().code
        data = adyen_structure_helper(
            order.adyen_reference,
            order.get_total_value(),
            order.email,
            f'client-{order.owner_id}',
            live_payment,
            'mx2LDQ39',
            shopper_locale=language,
            country_code=country_code,
            currency_code=currency_code,
        )
        redirect_url = (
            url + 'directory.shtml' + '?' + urlencode(data) + '&countryCode=' + language
        )
        try:
            r = requests.get(redirect_url)
            return Response(r.json())
        except ConnectionError as e:
            message = 'Connection to {0} failed. \n {1}'.format(
                url, e.args[0].args[1].args[1]
            )
            logger.exception(message)
            return Response(status=status.HTTP_503_SERVICE_UNAVAILABLE)
        except Exception:
            logger.exception('Error while connecting to Adyen')
        return Response(status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True)
    def payment_web_url(self, request, pk=None):
        return self.payment_view_core(
            live_payment=True,
            web_skin=True,
            request=request,
        )

    @action(detail=True)
    def payment_url_live(self, request, pk=None):
        return self.payment_view_core(
            live_payment=True,
            web_skin=False,
            request=request,
        )

    @action(detail=True)
    def payment_url(self, request, pk=None):
        return self.payment_view_core(
            live_payment=False,
            web_skin=False,
            request=request,
        )

    def payment_view_core(self, live_payment=False, web_skin=False, request=None):
        order = self.get_object()
        language = self._get_language()
        if GlobalSettings.live_payment_switch() is not True:
            live_payment = False
        order.change_status(OrderStatus.PAYMENT_PENDING)
        if not order.order_pretty_id:
            order.create_pretty_id()
        order.save()

        if order.region_promo_amount > 0 and order.get_total_value() == 0:
            order.update_after_promocode_for_whole_amount(request)
            return Response({'redirect_url': 'none', 'go_to_confirmation': True})

        adyen_settings = get_current_payment_settings(live=live_payment)
        url = 'https://{environment}.adyen.com/hpp/'.format(
            environment=adyen_settings.get('ENVIRONMENT')
        )

        country = Countries.get_country_by_name(
            order.country if order.country else Countries.united_kingdom
        )
        if country is None:
            country = Countries.united_kingdom
        country_code = country.code
        region = order.get_region()
        currency_code = region.get_currency().code

        if web_skin is True:
            data = adyen_structure_helper(
                order.adyen_reference,
                order.get_total_value(),
                order.email,
                f'client-{order.owner_id}',
                live_payment,
                'mx2LDQ39',
                shopper_locale=language,
                ship_days=order.get_longest_delivery_time_in_weeks() * 7,
                country_code=country_code,
                currency_code=currency_code,
            )
        else:
            data = adyen_structure_helper(
                order.adyen_reference,
                order.get_total_value(),
                order.email,
                f'client-{order.owner_id}',
                live_payment,
                shopper_locale=language,
                ship_days=order.get_longest_delivery_time_in_weeks() * 7,
                country_code=country_code,
                currency_code=currency_code,
            )

        redirect_url = url + 'select.shtml' + '?' + urlencode(data)
        transaction = Transaction()
        transaction.order = order
        transaction.merchant_reference = data['merchantReference']
        transaction.amount = int(float(data['paymentAmount']))
        transaction.save()
        if web_skin is True:
            return HttpResponseRedirect(redirect_url)
        else:
            return Response(
                {'redirect_url': redirect_url}
            )  # uniwebview://payment-complete?authResult=AUTHORISATION

    @action(methods=['post'], permission_classes=[IsOwner], detail=True)
    def cancel_order(self, request, pk=None):
        order = self.get_object()
        if order.status in [
            OrderStatus.PAYMENT_FAILED,
            OrderStatus.DRAFT,
            OrderStatus.PAYMENT_PENDING,
        ]:  # whut?!
            if order.status is OrderStatus.PAYMENT_PENDING:
                cart = CartService.get_or_create_cart(order.owner)
                cart_service = CartService(cart=cart)

                for item in order.items.all():
                    sellable_item = item.sellable_item
                    sellable_item.furniture_status = FurnitureStatusEnum.DRAFT
                    sellable_item.save(update_fields=['furniture_status'])

                    cart_service.add_to_cart(sellable_item)

            order.change_status(OrderStatus.CANCELLED)
            order.save()
            for suborder in order.suborders.all():
                suborder.change_status(OrderStatus.CANCELLED)
                suborder.save()
            return Response(status=status.HTTP_200_OK)
        else:
            return Response(status=status.HTTP_400_BAD_REQUEST)

    def _change_status(self, order, new_status, allowed_former_statuses):
        if order.status == new_status:
            # don't change it if it's already in expected status
            return Response(status=status.HTTP_200_OK)
        elif order.status not in allowed_former_statuses:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        order.change_status(new_status)
        return Response(status=status.HTTP_200_OK)

    @action(
        methods=['post'], permission_classes=[IsAuthenticated, IsOwner], detail=True
    )
    def change_status_to_pending(self, request, pk=None):
        process_delivery_buckets.delay(pk)
        send_order_confirmation_email_messages.delay(pk)
        order = self.get_object()
        order.emit_payment_related_events()

        return self._change_status(
            order=order,
            new_status=OrderStatus.PAYMENT_PENDING,
            allowed_former_statuses=[
                OrderStatus.CART,
                OrderStatus.DRAFT,
                OrderStatus.PAYMENT_FAILED,
            ],
        )

    @action(
        methods=['post'], permission_classes=[IsAuthenticated, IsOwner], detail=True
    )
    def change_status_to_draft(self, request, pk=None):
        order = self.get_object()
        return self._change_status(
            order=order,
            new_status=OrderStatus.DRAFT,
            allowed_former_statuses=[
                OrderStatus.CART,
                OrderStatus.DRAFT,
                OrderStatus.PAYMENT_FAILED,
                OrderStatus.PAYMENT_PENDING,
            ],
        )

    @action(methods=['post'], detail=True)
    def preview_gtm_purchase_event(self, request, pk=None):
        """
        Preview server side "purchase" event for Google Tag Manager.
        """
        preview_header = request.data.get('preview_header', None)
        if not preview_header:
            return Response(
                'Missing preview header.',
                status=status.HTTP_400_BAD_REQUEST,
            )

        order = self.get_object()
        serializer = GTMPurchaseEventSerializer(order, context={'request': request})
        event_data = {'data': [serializer.data]}
        internal_response = send_purchase_event_data_to_gtm(
            event_data,
            preview_header=preview_header,
        )
        return Response(internal_response.text, status=internal_response.status_code)

    @action(methods=['patch'], detail=True)
    def update_payment_method(self, request, pk=None):
        """
        TODO: investigate why updating with regualr PATCH was overriding the
        order.status field with old values. Investigate this before merging new
        checkout!
        """
        order = self.get_object()
        payment_method = request.data.get('chosen_payment_method', None)
        if not payment_method:
            return Response(
                'Missing payment method.',
                status=status.HTTP_400_BAD_REQUEST,
            )
        order.chosen_payment_method = payment_method
        order.save(update_fields=['chosen_payment_method'])
        return Response(status=status.HTTP_200_OK)


class EcommerceOrderViewSet(RetrieveModelMixin, GenericViewSet):
    permission_classes = (IsAuthenticated,)
    serializer_class = EcommerceOrderSerializer

    def get_queryset(self):
        return Order.objects.filter()

    def get_serializer_context(self):
        order = self.get_object()
        cached_region_data = order.region.cached_region_data
        strikethrough_promo_data = get_strikethrough_promo_data(order)
        strikethrough_voucher = strikethrough_promo_data.strikethrough_voucher
        has_strikethrough_promo_applied = (
            strikethrough_promo_data.has_strikethrough_promo_applied
        )
        active_promotion = get_active_promotion(cached_region_data)
        active_promotion_config = (
            active_promotion.configs.last() if active_promotion else None
        )

        return {
            **super().get_serializer_context(),
            'language': get_language_from_request(self.request),
            'region': cached_region_data,
            'strikethrough_voucher': strikethrough_voucher,
            'has_strikethrough_promo_applied': has_strikethrough_promo_applied,
            'active_promotion': active_promotion,
            'active_promotion_config': active_promotion_config,
            'region_calculations_object': (
                RegionCalculationsObject(region=cached_region_data)
            ),
        }


class RetoolOrderViewSet(ModelViewSet):
    """
    Retool is an external service used to build GUI. This viewset is a port of a larger
    project aimd at creating B2B Panel, that allows to create and proceed business
    orders.
    """

    serializer_class = OrderRetoolSerializer
    permission_classes = (IsAdminUser,)
    authentication_classes = (TokenAuthentication,)
    queryset = Order.objects.filter(parent_order__isnull=True)

    def perform_update(self, serializer):
        serializer.save()

        order = self.get_object()
        cart_service = CartService(cart=order.cart)
        cart_service.recalculate_cart(order.cart, check_vat=True)
        cart_service.sync_with_order()

    def get_queryset(self):
        return super().get_queryset().filter(owner_id=self._owner_id)

    @property
    def _owner_id(self) -> Optional[str]:
        user_id = self.request.GET.get('user_id', None) or self.request.data.get(
            'user_id', None
        )

        if not user_id:
            raise ValidationError('"user_id" param is required')
        return user_id

    def get_user(self) -> User:
        return get_object_or_404(User, pk=self._owner_id)

    @action(methods=['POST'], detail=False)
    def create_cart_order(self, request, **kwargs):
        cart = CartService.create_cart(user=self.get_user())
        cart_service = CartService(cart)
        order = cart_service.sync_with_order(source=OrderSource.INTERNAL)
        serializer = self.get_serializer(order)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(methods=['POST'], detail=True)
    def add_item_to_order(self, request, **kwargs):
        order = self.get_object()
        cart_service = CartService(cart=order.cart)

        serializer = RetoolCopyItemSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        for _ in range(serializer.validated_data['quantity']):
            instance = serializer.save(order=order)
            try:
                cart_service.add_to_cart(instance)
            except MaxCartSizeError:
                return Response(
                    {'error': 'cart_max_size'}, status=status.HTTP_400_BAD_REQUEST
                )

        cart_service.sync_with_order()
        return Response('ok', status=status.HTTP_201_CREATED)

    @action(methods=['POST'], detail=True)
    def apply_voucher(self, request, **kwargs):
        order = self.get_object()
        cart_service = CartService(cart=order.cart)

        voucher_code = request.data.get('voucher_code', None)
        if not voucher_code:
            cart_service.reset_promo()
            cart_service.sync_with_order()
            return Response(status=status.HTTP_200_OK)

        voucher_service = VoucherService(
            instance=order.cart, code=voucher_code.rstrip()
        )
        return_data = voucher_service.process_voucher()
        cart_service.sync_with_order()
        return Response(
            return_data,
            status=(
                status.HTTP_200_OK
                if return_data.get('status') == 'ok'
                else status.HTTP_400_BAD_REQUEST
            ),
        )

    @action(methods=['DELETE'], detail=True)
    def delete_item(self, request, **kwargs):
        order = self.get_object()
        cart_service = CartService(cart=order.cart)

        order_item_id = request.data.get('order_item_id', None)
        try:
            order_item = order.items.get(id=order_item_id)
        except OrderItem.DoesNotExist:
            return Response(
                data={
                    'message': f'Object with id {order_item_id} is not in given order.',
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        cart_service.delete_from_cart(
            order_item.object_id,
            order_item.content_type.model,
        )
        cart_service.sync_with_order()
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(methods=['POST'], detail=True)
    def create_proforma_invoice(self, request, pk):
        order = self.get_object()
        if not order.order_pretty_id:
            order.create_pretty_id()  # order with proforma should have this
        create_proforma_invoice_task.delay(pk)
        return Response(status=status.HTTP_200_OK)

    @action(methods=['GET'], detail=True)
    def get_invoices(self, request, **kwargs):
        order = self.get_object()
        invoices = order.invoice_set.all()
        return Response(
            data=RetoolInvoiceSerializer(invoices, many=True).data,
            status=status.HTTP_200_OK,
        )

    @action(methods=['POST'], detail=True)
    def change_assembly_service(self, request, **kwargs):
        order = self.get_object()
        assembly = not order.assembly

        cart_service = CartService(cart=order.cart)
        cart_service.change_assembly(assembly)
        cart_service.sync_with_order()

        return Response(status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=True)
    def change_region(self, request, **kwargs):
        order = self.get_object()
        try:
            region = Region.objects.get(name=request.data['region_name'])
        except Region.DoesNotExist:
            raise ValidationError('Given region does not exist.')

        order.owner.profile.change_region(region)
        cart_service = CartService(order.cart)
        cart_service.change_region(region)
        cart_service.sync_with_order()

        return Response(status=status.HTTP_200_OK)


class LastSale(APIView):
    permission_classes = ()

    def get(self, request, **kwargs):
        last_order = (
            Order.objects.filter(
                status=OrderStatus.IN_PRODUCTION,
                paid_at__isnull=False,
            )
            .order_by('-paid_at')
            .first()
        )
        created_at = int(mktime(last_order.paid_at.timetuple()))
        oi = last_order.items.all().order_by('-price').first()
        project_id = oi.order_item.get_project_id()
        return HttpResponse(
            f'<{created_at} {project_id}>',
            content_type='text/plain',
            status=status.HTTP_200_OK,
        )


class CheckOrderStatusViewSet(ListModelMixin, GenericViewSet):
    serializer_class = CheckOrderStatusFiltersSerializer
    authentication_classes = (TokenAuthentication, SessionAuthentication)
    queryset = Order.objects.all()
    permission_classes = (AllowAny,)

    def get_object(self):
        serializer = self.get_serializer(data=self.request.query_params)
        serializer.is_valid(raise_exception=True)
        sanitised_postal_code = sanitise_postal_code(
            serializer.validated_data.pop('postal_code')
        )
        try:
            order = Order.objects.get(**serializer.validated_data)
        except Order.DoesNotExist:
            raise ValidationError(code='incorrect_order_data')
        else:
            postal_codes = (
                (
                    order.sanitised_postal_code.upper()
                    if order.sanitised_postal_code
                    else None
                ),
                (
                    order.sanitised_invoice_postal_code.upper()
                    if order.sanitised_invoice_postal_code
                    else None
                ),
            )
            if sanitised_postal_code not in postal_codes:
                raise ValidationError(code='incorrect_order_data')
            return order

    def list(self, request, *args, **kwargs):
        try:
            order = self.get_object()
        except ValidationError:
            self.save_to_history(success=False)
            return Response(
                {
                    'order_status_code': None,
                    'status': get_status_description(
                        'no_order', None, empty_lines=False
                    ),
                }
            )
        prefetch_related_objects([order], 'product_set')
        serializer = OrderStatusSerializer(
            order, context={'request': request, 'order': order}
        )
        self.save_to_history(success=True, order=order)
        return Response(serializer.data)

    def save_to_history(self, success, order=None):
        data = {
            'success': success,
        }
        if order:
            data['order_status'] = order.status
        query_params_serializer = CheckOrderStatusSerializer(
            data=self.request.query_params
        )
        query_params_serializer.is_valid()
        data.update(query_params_serializer.validated_data)
        serializer = CheckOrderStatusHistorySerializer(data=data)
        if serializer.is_valid():
            serializer.save()


class CheckOrdersStatusByEmailViewSet(
    RetrieveModelMixin,
    ListModelMixin,
    GenericViewSet,
):
    serializer_class = OrderStatusSerializer
    authentication_classes = (TokenAuthentication, SessionAuthentication)
    queryset = Order.objects.all()
    filter_backends = [DjangoFilterBackend]
    filterset_class = OrderByEmailFilter

    def get_serializer_context(self):
        context = super().get_serializer_context()
        order = self.filter_queryset(self.get_queryset()).first()
        prefetch_related_objects([order], 'product_set')
        context['order'] = order
        return context


class MailingOrderSummaryAPIView(RetrieveModelMixin, GenericViewSet):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (BrazePermission,)
    queryset = (
        Order.objects.exclude(order_type=OrderType.COMPLAINT)
        .prefetch_related(
            'items',
            'items__order_item',
            'product_set',
            'product_set__product_details_jetty',
        )
        .select_related(
            'owner',
            'owner__profile',
            'owner__profile__region',
        )
    )
    serializer_class = MailingOrderSummarySerializer

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        language = LanguageEnum(instance.owner.profile.language)

        translation.activate(language)

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def get_serializer_context(self):
        return {
            'region': self.get_object().owner.profile.region,
            **super().get_serializer_context(),
        }
